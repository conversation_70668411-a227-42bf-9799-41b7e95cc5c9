import { useCallback, useMemo, useState } from 'react';
import { getAllMeetingTemplates } from '@shared/utils/api/template';
import { QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import type { BETemplateResponse } from '@shared/utils/api/template';
import type { PaginateResponse } from '@shared/types/response';

export interface MeetingTemplateSearchParams {
  text?: string;
  page?: number;
  size?: number;
}

export interface NormalizedMeetingTemplate {
  id: string;
  title: string;
  subject: string;
  message: string;
  fileIds: string[];
  default: boolean;
  createdDate: string;
  userId: string;
  pageId: string;
}

const defaultTransformTemplate = (template: BETemplateResponse): NormalizedMeetingTemplate => ({
  id: template.id,
  title: template.title,
  subject: template.subject,
  message: template.message,
  fileIds: template.fileIds || [],
  default: false, // Meeting templates don't have default field in the same way
  createdDate: template.createdDate,
  userId: template.userId,
  pageId: template.pageId,
});

export default function useMeetingTemplates() {
  const [searchParams, setSearchParams] = useState<MeetingTemplateSearchParams>({
    text: '',
    page: 0,
    size: 20,
  });

  const meetingTemplatesQuery = useReactQuery<PaginateResponse<BETemplateResponse>>({
    action: {
      key: [QueryKeys.getAllMeetingTemplates, searchParams],
      apiFunc: () => getAllMeetingTemplates(searchParams),
    },
  });

  const templates = useMemo(() => {
    if (!meetingTemplatesQuery.data?.content) return [];
    return meetingTemplatesQuery.data.content.map(defaultTransformTemplate);
  }, [meetingTemplatesQuery.data]);

  const filteredTemplates = useMemo(() => {
    if (!searchParams.text?.trim()) return templates;
    
    const searchTerm = searchParams.text.toLowerCase();
    return templates.filter(
      (template) =>
        template.title.toLowerCase().includes(searchTerm) ||
        template.subject.toLowerCase().includes(searchTerm) ||
        template.message.toLowerCase().includes(searchTerm)
    );
  }, [templates, searchParams.text]);

  const updateSearchParams = useCallback(
    (newParams: Partial<MeetingTemplateSearchParams>) => {
      setSearchParams((prev) => ({
        ...prev,
        ...newParams,
      }));
    },
    []
  );

  const handleSearchChange = useCallback(
    (query: string) => {
      updateSearchParams({ text: query, page: 0 });
    },
    [updateSearchParams]
  );

  const clearSearch = useCallback(() => {
    updateSearchParams({ text: '', page: 0 });
  }, [updateSearchParams]);

  const getTemplateById = useCallback(
    (id: string) => templates.find((template) => template.id === id),
    [templates]
  );

  return {
    templates: filteredTemplates,
    allTemplates: templates,
    isLoading: meetingTemplatesQuery.isLoading,
    error: meetingTemplatesQuery.error,
    searchQuery: searchParams.text || '',
    hasResults: filteredTemplates.length > 0,
    totalCount: templates.length,
    filteredCount: filteredTemplates.length,
    totalElements: meetingTemplatesQuery.data?.totalElements || 0,
    totalPages: meetingTemplatesQuery.data?.totalPages || 0,
    refetch: meetingTemplatesQuery.refetch,
    handleSearchChange,
    clearSearch,
    updateSearchParams,
    getTemplateById,
    isFiltered: (searchParams.text?.trim()?.length || 0) > 0,
  };
}
