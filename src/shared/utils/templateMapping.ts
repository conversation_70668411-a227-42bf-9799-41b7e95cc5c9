import type { NormalizedMeetingTemplate } from '@shared/hooks/schedules/useMeetingTemplates';

/**
 * Interface for form data that expects template data
 * This matches the structure used in meeting forms and other contexts
 */
export interface MeetingFormTemplateData {
  templateName?: string;
  subject?: string;
  message?: string;
  description?: string; // Some forms might use 'description' instead of 'message'
  [key: string]: any; // Allow for additional fields
}

/**
 * Interface for TemplateForm component data structure
 */
export interface TemplateFormData {
  templateName: string;
  subject: string;
  message: string;
  delay: string;
  attachments: any[];
  hasFollowup?: boolean;
  followupPeriod?: string;
  followupTitle?: string;
  followupMessage?: string;
  followupAttachments?: any[];
  default?: boolean;
}

/**
 * Maps meeting template data to form field structure
 * Handles the mapping from template fields to form fields:
 * - title -> templateName
 * - subject -> subject (direct mapping)
 * - message -> message (direct mapping)
 */
export const mapMeetingTemplateToFormData = (
  template: NormalizedMeetingTemplate
): Partial<MeetingFormTemplateData> => ({
  templateName: template.title,
  subject: template.subject,
  message: template.message,
  description: template.message, // Some forms might use 'description' field
});

/**
 * Maps meeting template data to TemplateForm component structure
 * This is used when displaying the template in the TemplateForm component
 */
export const mapMeetingTemplateToTemplateFormData = (
  template: NormalizedMeetingTemplate,
  defaultDelay: string = 'IMMEDIATELY'
): TemplateFormData => ({
  templateName: template.title,
  subject: template.subject,
  message: template.message,
  delay: defaultDelay,
  attachments: [], // Meeting templates don't have attachments in the same structure
  hasFollowup: false, // Meeting templates don't have followup
  followupPeriod: undefined,
  followupTitle: undefined,
  followupMessage: undefined,
  followupAttachments: undefined,
  default: template.default,
});

/**
 * Maps template data to meeting description field specifically
 * This is useful when only updating the description field of a meeting form
 */
export const mapTemplateToDescription = (
  template: NormalizedMeetingTemplate
): string => template.message;

/**
 * Maps template data to meeting subject field specifically
 * This is useful when only updating the subject field of a meeting form
 */
export const mapTemplateToSubject = (
  template: NormalizedMeetingTemplate
): string => template.subject;

/**
 * Generic mapping function that can be customized for different form structures
 */
export const mapTemplateToCustomFields = (
  template: NormalizedMeetingTemplate,
  fieldMapping: Record<string, string>
): Record<string, any> => {
  const result: Record<string, any> = {};
  
  Object.entries(fieldMapping).forEach(([formField, templateField]) => {
    if (templateField in template) {
      result[formField] = (template as any)[templateField];
    }
  });
  
  return result;
};

/**
 * Default field mapping for meeting templates
 */
export const DEFAULT_MEETING_TEMPLATE_FIELD_MAPPING = {
  templateName: 'title',
  subject: 'subject',
  message: 'message',
  description: 'message',
} as const;
