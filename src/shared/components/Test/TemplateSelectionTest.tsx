import React from 'react';
import { Formik, Form } from 'formik';
import Flex from '@shared/uikit/Flex';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import { useMeetingFormFields } from '@shared/hooks/schedules/useMeetingFormFields';
import useTranslation from '@shared/utils/hooks/useTranslation';

interface TestFormValues {
  title?: string;
  subject?: string;
  description?: string;
  [key: string]: any;
}

const TemplateSelectionTest: React.FC = () => {
  const { t } = useTranslation();
  const initialValues: TestFormValues = {
    title: '',
    subject: '',
    description: '',
  };

  return (
    <Flex className="p-20">
      <Formik
        initialValues={initialValues}
        onSubmit={(values) => {
          console.log('Form submitted with values:', values);
        }}
      >
        {(formikProps) => {
          // Use the hook inside the Formik context
          const { description } = useMeetingFormFields();

          const formGroups = [
            {
              name: 'title',
              cp: 'input',
              label: t('title'),
              required: false,
            },
            {
              name: 'subject',
              cp: 'input',
              label: t('subject'),
              required: false,
            },
            description,
          ];

          return (
            <Form>
              <Flex className="flex-col gap-20">
                <h2 className="text-xl font-bold">Template Selection Test</h2>
                <p className="text-sm text-gray-600">
                  Click the "Choose Template" button in the description field to test the template selection workflow.
                </p>
                <DynamicFormBuilder groups={formGroups} />
                <div className="mt-20">
                  <h3 className="text-lg font-semibold mb-10">Current Form Values:</h3>
                  <pre className="bg-gray-100 p-10 rounded text-sm">
                    {JSON.stringify(formikProps.values, null, 2)}
                  </pre>
                </div>
              </Flex>
            </Form>
          );
        }}
      </Formik>
    </Flex>
  );
};

export default TemplateSelectionTest;
