# Template Selection Workflow

This document describes the template selection workflow implementation that allows users to select meeting templates and populate form fields with template data.

## Components

### 1. TemplateSelectionModal
**Location**: `src/shared/components/Organism/TemplateSelectionModal/TemplateSelectionModal.tsx`

A modal component that displays a list of meeting templates for selection.

**Props**:
- `isOpen: boolean` - Controls modal visibility
- `onClose: () => void` - Callback when modal is closed
- `onTemplateSelect: (template: NormalizedMeetingTemplate) => void` - Callback when a template is selected

**Features**:
- Uses the existing `TemplateList` component with actions disabled
- Fetches meeting templates using `useMeetingTemplates` hook
- Supports search functionality
- Transforms meeting templates to match TemplateList expected format

### 2. useMeetingTemplates Hook
**Location**: `src/shared/hooks/schedules/useMeetingTemplates.ts`

A custom hook for fetching and managing meeting templates.

**Returns**:
- `templates: NormalizedMeetingTemplate[]` - Filtered templates
- `allTemplates: NormalizedMeetingTemplate[]` - All templates
- `isLoading: boolean` - Loading state
- `searchQuery: string` - Current search query
- `handleSearchChange: (query: string) => void` - Search handler
- `getTemplateById: (id: string) => NormalizedMeetingTemplate | undefined` - Get template by ID
- Other utility functions for pagination and filtering

### 3. Template Data Mapping
**Location**: `src/shared/utils/templateMapping.ts`

Utility functions for mapping template data to form field structures.

**Key Functions**:
- `mapMeetingTemplateToFormData(template)` - Maps template to form data
- `mapMeetingTemplateToTemplateFormData(template)` - Maps to TemplateForm structure
- `mapTemplateToDescription(template)` - Extracts description field
- `mapTemplateToSubject(template)` - Extracts subject field

## Integration

### useMeetingFormFields Hook Integration

The `useMeetingFormFields` hook has been updated to include template selection functionality:

```typescript
const {
  description, // Now includes template selection button and modal
  templateModal, // Template modal utilities
  // ... other form fields
} = useMeetingFormFields(permissions);
```

The `description` field now includes:
- A "Choose Template" button that opens the template selection modal
- The TemplateSelectionModal component embedded in the field
- Automatic form field population when a template is selected

### Data Flow

1. User clicks "Choose Template" button in the description field
2. TemplateSelectionModal opens and displays available meeting templates
3. User searches and selects a template
4. Template data is mapped using `mapMeetingTemplateToFormData`
5. Form fields are automatically populated:
   - `description` field gets template message
   - `subject` field gets template subject
   - `title` field gets template title (templateName)
6. Modal closes automatically after selection

## Field Mapping

Meeting templates are mapped to form fields as follows:

| Template Field | Form Field | Description |
|----------------|------------|-------------|
| `title` | `templateName` | Template name |
| `subject` | `subject` | Email/meeting subject |
| `message` | `description` | Main content/description |

## Usage Example

```typescript
import { useMeetingFormFields } from '@shared/hooks/schedules/useMeetingFormFields';

const MyMeetingForm = () => {
  const { description, templateModal } = useMeetingFormFields();
  
  // The description field automatically includes template selection
  const formGroups = [
    // ... other fields
    description, // Includes "Choose Template" button and modal
  ];
  
  return (
    <Form>
      <DynamicFormBuilder groups={formGroups} />
    </Form>
  );
};
```

## Testing

A test component is available at `src/shared/components/Test/TemplateSelectionTest.tsx` that demonstrates the complete workflow.

## Notes

- The implementation reuses existing components (`TemplateList`, `ModalDialog`, etc.)
- Template actions (3-dots menu) are disabled in selection mode
- The workflow is integrated seamlessly into existing form structures
- All TypeScript types are properly defined and exported
- The implementation follows the existing codebase patterns and conventions
