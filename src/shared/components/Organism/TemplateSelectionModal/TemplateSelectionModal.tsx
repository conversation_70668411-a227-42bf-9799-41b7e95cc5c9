import React from 'react';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalDialog from '@shared/uikit/Modal/ModalDialog';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import useTranslation from '@shared/utils/hooks/useTranslation';
import useMeetingTemplates from '@shared/hooks/schedules/useMeetingTemplates';
import type { NormalizedMeetingTemplate } from '@shared/hooks/schedules/useMeetingTemplates';
import TemplateList from '../AutomationModal/components/TemplateList';
import type { NormalizedTemplate } from '../AutomationModal/types/template.types';

export interface TemplateSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onTemplateSelect: (template: NormalizedMeetingTemplate) => void;
}

// Transform meeting template to match TemplateList expected format
const transformMeetingTemplateToNormalized = (
  meetingTemplate: NormalizedMeetingTemplate
): NormalizedTemplate => ({
  id: meetingTemplate.id,
  title: meetingTemplate.title,
  subject: meetingTemplate.subject,
  message: meetingTemplate.message,
  fileIds: meetingTemplate.fileIds,
  hasFollowup: false, // Meeting templates don't have followup
  default: meetingTemplate.default,
  followupPeriod: undefined,
  followupTitle: undefined,
  followupMessage: undefined,
  followupAttachments: undefined,
});

const TemplateSelectionModal: React.FC<TemplateSelectionModalProps> = ({
  isOpen,
  onClose,
  onTemplateSelect,
}) => {
  const { t } = useTranslation();
  const {
    templates,
    isLoading,
    searchQuery,
    handleSearchChange,
    getTemplateById,
  } = useMeetingTemplates();

  const handleTemplateClick = (templateId: string) => {
    const template = getTemplateById(templateId);
    if (template) {
      onTemplateSelect(template);
      onClose();
    }
  };

  // Transform meeting templates to match TemplateList expected format
  const normalizedTemplates = templates.map(transformMeetingTemplateToNormalized);

  return (
    <ModalDialog
      isOpen={isOpen}
      onClose={onClose}
      variant="simple"
      showConfirm={false}
    >
      <ModalHeaderSimple
        title={t('choose_template')}
        onClose={onClose}
      />
      <ModalBody>
        <TemplateList
          templates={normalizedTemplates}
          isLoading={isLoading}
          searchQuery={searchQuery}
          onSearchChange={handleSearchChange}
          onTemplateClick={handleTemplateClick}
          config={{
            showSearch: true,
            showActions: false, // Remove 3-dots menu/actions
            showDefaultToggle: false, // Remove default toggle for selection
            emptyStateMessage: t('no_meeting_templates_found'),
          }}
        />
      </ModalBody>
    </ModalDialog>
  );
};

export default TemplateSelectionModal;
